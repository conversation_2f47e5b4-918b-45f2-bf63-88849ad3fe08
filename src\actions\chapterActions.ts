'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { revalidatePath } from 'next/cache';
import { Prisma } from '@/generated/prisma';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { uploadPageImage, deleteImage, extractFilePathFromUrl, isSupabaseStorageUrl } from '@/lib/supabaseStorage';
import { CreateChapterSchema } from '@/lib/schemas';
import { ChapterWithMangaSeries } from '@/types';
// Import custom Prisma types if needed in the future
// import { PageCreateManyInput } from '@/types/prisma';

// Helper function to clean up uploaded images
async function cleanupUploadedImages(imagePaths: string[]) {
  for (const imagePath of imagePaths) {
    try {
      await deleteImage('PAGES', imagePath);
    } catch (error) {
      console.error('Failed to cleanup uploaded image:', imagePath, error);
    }
  }
}

type CreateChapterState = {
  errors?: {
    mangaSeriesId?: string[];
    chapterNumber?: string[];
    title?: string[];
    pages?: string[];
    _form?: string[]; // General form errors
  };
  message?: string | null;
};

export async function createChapter(
  prevState: CreateChapterState,
  formData: FormData
): Promise<CreateChapterState> {
  const session = await auth();
  if (!session?.user) {
    return {
      errors: { _form: ['Authentication required'] },
      message: 'Authentication failed.',
    };
  }

  const validatedFields = CreateChapterSchema.safeParse({
    mangaSeriesId: formData.get('mangaSeriesId'),
    chapterNumber: formData.get('chapterNumber'),
    title: formData.get('title'),
    pages: formData.getAll('pages'),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: 'Validation failed. Please check the fields.',
    };
  }

  const { mangaSeriesId, chapterNumber, title } = validatedFields.data;
  const pageFiles = formData.getAll('pages') as File[];

  if (!pageFiles || pageFiles.length === 0 || pageFiles.some(file => file.size === 0)) {
    return {
      errors: { pages: ['At least one page image is required.'] },
      message: 'Missing page images.',
    };
  }

  pageFiles.sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }));

  // Define the page data structure inline instead of using Prisma namespace type
  const pageDataToCreate: {
    pageNumber: number;
    imagePath: string;
    chapterId: string;
  }[] = [];
  let newChapterId: string | null = null; // To store the ID for potential cleanup
  const uploadedImages: string[] = []; // Track uploaded images for cleanup

  try {

    const newChapter = await prisma.chapter.create({
      data: {
        mangaSeriesId: mangaSeriesId,
        chapterNumber: chapterNumber,
        title: title || null,
      },
    });
    newChapterId = newChapter.id; // Store ID

    for (let i = 0; i < pageFiles.length; i++) {
      const file = pageFiles[i];
      const pageNumber = i + 1;

      if (!file.type.startsWith('image/')) {
         // Attempt cleanup before returning error
        if (newChapterId) {
            await prisma.chapter.delete({ where: { id: newChapterId } }).catch((e: Error) => console.error("Cleanup failed:", e));
            // Clean up any uploaded images
            await cleanupUploadedImages(uploadedImages);
        }
        return {
          errors: { pages: [`File "${file.name}" is not a valid image type.`] },
          message: 'Invalid file type detected.',
        };
      }

      // Upload to Supabase Storage
      const uploadResult = await uploadPageImage(file, mangaSeriesId, chapterNumber, pageNumber);

      if (!uploadResult.success) {
        // Attempt cleanup before returning error
        if (newChapterId) {
            await prisma.chapter.delete({ where: { id: newChapterId } }).catch((e: Error) => console.error("Cleanup failed:", e));
            // Clean up any uploaded images
            await cleanupUploadedImages(uploadedImages);
        }
        return {
          errors: { pages: [uploadResult.error || `Failed to upload "${file.name}".`] },
          message: 'Image upload failed.',
        };
      }

      // Track uploaded image for potential cleanup
      if (uploadResult.path) {
        uploadedImages.push(uploadResult.path);
      }

      pageDataToCreate.push({
        pageNumber: pageNumber,
        imagePath: uploadResult.url || '',
        chapterId: newChapter.id,
      });
    }

    if (pageDataToCreate.length > 0) {
      await prisma.page.createMany({
        data: pageDataToCreate,
      });
    }

    // --- Revalidation should happen AFTER all DB operations succeed ---
    revalidatePath(`/dashboard/series/${mangaSeriesId}/edit`);
    revalidatePath('/dashboard/series');
    revalidatePath('/dashboard/chapters');
    // revalidateTag(`series-chapters-${mangaSeriesId}`); // Keep commented unless needed

    return {
      message: `Chapter "${chapterNumber}" created successfully with ${pageDataToCreate.length} pages.`,
    };

  } catch (error: unknown) {
    console.error('Error during chapter creation or file upload:', error);
     // Attempt cleanup if chapter was created before error
    if (newChapterId) {
        await prisma.chapter.delete({ where: { id: newChapterId } }).catch((e: Error) => console.error("Cleanup failed:", e));
        // Clean up any uploaded images
        await cleanupUploadedImages(uploadedImages);
    }

    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
      return { errors: { _form: ['A chapter with this number already exists for this series.'] }, message: 'Chapter creation failed.' };
    }
    return {
      errors: { _form: ['An error occurred during chapter creation or file upload.'] },
      message: 'Failed to create chapter.',
    };
  }
}

/**
 * Fetch a single chapter by ID, including its pages
 */
export async function getChapterById(id: string) {
  const session = await auth();
  if (!session?.user) {
    // Or throw an error, depending on how you want to handle unauthorized access
    return null;
  }

  const chapter = await prisma.chapter.findUnique({
    where: { id },
    include: {
      pages: {
        orderBy: {
          pageNumber: 'asc',
        },
      },
      mangaSeries: { // Include series info for context
        select: {
          id: true,
          title: true,
        }
      }
    },
  });

  return chapter;
}

// Type for updateChapterDetails state
type UpdateChapterDetailsState = {
  errors?: {
    chapterNumber?: string[];
    title?: string[];
    _form?: string[];
  };
  message?: string | null;
};

// Zod schema for updating chapter details
const UpdateChapterDetailsSchema = z.object({
  chapterNumber: z.string().min(1, 'Chapter number is required'),
  title: z.string().optional(),
});

/**
 * Update chapter details (number, title)
 */
export async function updateChapterDetails(
  chapterId: string,
  prevState: UpdateChapterDetailsState,
  formData: FormData
): Promise<UpdateChapterDetailsState> {
  const session = await auth();
  if (!session?.user) {
    return { errors: { _form: ['Authentication required'] }, message: 'Authentication failed.' };
  }

  if (!chapterId) {
    return { errors: { _form: ['Chapter ID is missing.'] }, message: 'Update failed.' };
  }

  const validatedFields = UpdateChapterDetailsSchema.safeParse({
    chapterNumber: formData.get('chapterNumber'),
    title: formData.get('title'),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: 'Validation failed.',
    };
  }

  const { chapterNumber, title } = validatedFields.data;

  try {
    const updatedChapter = await prisma.chapter.update({
      where: { id: chapterId },
      data: {
        chapterNumber: chapterNumber,
        title: title || null,
      },
    });

    // Revalidate the chapter edit page and potentially the series page
    revalidatePath(`/dashboard/chapters/${chapterId}/edit`);
    revalidatePath(`/dashboard/series/${updatedChapter.mangaSeriesId}/edit`);
    revalidatePath('/dashboard/chapters');

    return { message: 'Chapter details updated successfully.' };

  } catch (error: unknown) {
    console.error('Error updating chapter details:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
       // Handle potential unique constraint violation if chapter number needs to be unique per series
       // This depends on your schema definition
      return { errors: { chapterNumber: ['This chapter number already exists for this series.'] }, message: 'Update failed.' };
    }
    return { errors: { _form: ['Database error: Failed to update chapter.'] }, message: 'Update failed.' };
  }
}

/**
 * Delete a chapter and all its associated pages
 */
type DeleteChapterState = {
  success: boolean;
  message: string;
};

export async function deleteChapter(
  prevState: DeleteChapterState,
  formData: FormData
): Promise<DeleteChapterState> {
  const session = await auth();
  if (!session?.user) {
    return {
      success: false,
      message: 'Authentication required',
    };
  }

  const chapterId = formData.get('chapterId')?.toString();
  if (!chapterId) {
    return {
      success: false,
      message: 'Chapter ID is required for deletion',
    };
  }

  try {
    // First, get the chapter to find its series ID and pages for cleanup
    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      include: {
        pages: true,
      },
    });

    if (!chapter) {
      return {
        success: false,
        message: 'Chapter not found',
      };
    }

    const mangaSeriesId = chapter.mangaSeriesId;

    // Delete page image files from Supabase Storage
    if (chapter.pages.length > 0) {
      for (const page of chapter.pages) {
        if (page.imagePath && isSupabaseStorageUrl(page.imagePath)) {
          const filePath = extractFilePathFromUrl(page.imagePath);
          if (filePath) {
            const deleteResult = await deleteImage('PAGES', filePath);
            if (!deleteResult.success) {
              console.error('Error deleting page image from Supabase:', deleteResult.error);
              // Continue with other deletions even if one fails
            }
          }
        }
      }
    }

    // Delete the chapter (this will cascade delete pages due to the relation)
    await prisma.chapter.delete({
      where: { id: chapterId },
    });

    // Revalidate paths
    revalidatePath(`/dashboard/series/${mangaSeriesId}/edit`);
    revalidatePath('/dashboard/series');
    revalidatePath('/dashboard/chapters');

    return {
      success: true,
      message: `Chapter ${chapter.chapterNumber} deleted successfully.`,
    };
  } catch (error) {
    console.error('Error deleting chapter:', error);
    return {
      success: false,
      message: 'Failed to delete chapter. Please try again.',
    };
  }
}

/**
 * Delete a single page from a chapter
 */
type DeletePageState = {
  success: boolean;
  message: string;
};

export async function deletePage(
  prevState: DeletePageState,
  formData: FormData
): Promise<DeletePageState> {
  const session = await auth();
  if (!session?.user) {
    return {
      success: false,
      message: 'Authentication required',
    };
  }

  const pageId = formData.get('pageId')?.toString();
  if (!pageId) {
    return {
      success: false,
      message: 'Page ID is required for deletion',
    };
  }

  try {
    // First, get the page to find its chapter and image path for cleanup
    const page = await prisma.page.findUnique({
      where: { id: pageId },
      include: {
        chapter: {
          select: {
            id: true,
            chapterNumber: true,
            mangaSeriesId: true,
          },
        },
      },
    });

    if (!page) {
      return {
        success: false,
        message: 'Page not found',
      };
    }

    const chapterId = page.chapterId;

    // Delete the image file from Supabase Storage
    if (page.imagePath && isSupabaseStorageUrl(page.imagePath)) {
      const filePath = extractFilePathFromUrl(page.imagePath);
      if (filePath) {
        const deleteResult = await deleteImage('PAGES', filePath);
        if (!deleteResult.success) {
          console.error('Error deleting page image from Supabase:', deleteResult.error);
          // Continue with DB deletion even if file deletion fails
        }
      }
    }

    // Delete the page from the database
    await prisma.page.delete({
      where: { id: pageId },
    });

    // Revalidate paths
    revalidatePath(`/dashboard/chapters/${chapterId}/edit`);
    revalidatePath(`/dashboard/series/${page.chapter.mangaSeriesId}/edit`);
    revalidatePath('/dashboard/chapters');

    return {
      success: true,
      message: 'Page deleted successfully.',
    };
  } catch (error) {
    console.error('Error deleting page:', error);
    return {
      success: false,
      message: 'Failed to delete page. Please try again.',
    };
  }
}

/**
 * Reorder pages within a chapter
 */
type ReorderPagesState = {
  success: boolean;
  message: string;
};

interface PageOrderItem {
  id: string;
  newPageNumber: number;
}

const ReorderPagesSchema = z.object({
  chapterId: z.string().cuid(),
  pageOrder: z.array(
    z.object({
      id: z.string().cuid(),
      newPageNumber: z.number().int().positive(),
    })
  ),
});

export async function reorderPages(
  prevState: ReorderPagesState,
  formData: FormData
): Promise<ReorderPagesState> {
  const session = await auth();
  if (!session?.user) {
    return {
      success: false,
      message: 'Authentication required',
    };
  }

  // Parse the JSON string from formData
  const chapterId = formData.get('chapterId')?.toString();
  const pageOrderJson = formData.get('pageOrder')?.toString();

  if (!chapterId || !pageOrderJson) {
    return {
      success: false,
      message: 'Chapter ID and page order are required',
    };
  }

  let pageOrder: PageOrderItem[];
  try {
    pageOrder = JSON.parse(pageOrderJson);
  } catch (error) {
    return {
      success: false,
      message: 'Invalid page order format',
    };
  }

  // Validate with Zod
  const validationResult = ReorderPagesSchema.safeParse({
    chapterId,
    pageOrder,
  });

  if (!validationResult.success) {
    return {
      success: false,
      message: 'Invalid data format',
    };
  }

  try {
    // Check if chapter exists
    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      select: { id: true, mangaSeriesId: true },
    });

    if (!chapter) {
      return {
        success: false,
        message: 'Chapter not found',
      };
    }

    // Update each page with its new page number
    const updatePromises = pageOrder.map(({ id, newPageNumber }) =>
      prisma.page.update({
        where: { id },
        data: { pageNumber: newPageNumber },
      })
    );

    await Promise.all(updatePromises);

    // Revalidate paths
    revalidatePath(`/dashboard/chapters/${chapterId}/edit`);
    revalidatePath(`/dashboard/series/${chapter.mangaSeriesId}/edit`);
    revalidatePath('/dashboard/chapters');

    return {
      success: true,
      message: 'Pages reordered successfully.',
    };
  } catch (error) {
    console.error('Error reordering pages:', error);
    return {
      success: false,
      message: 'Failed to reorder pages. Please try again.',
    };
  }
}

/**
 * Fetch all chapters with their manga series information
 * Used for the dashboard chapters overview page
 */
export async function getAllChapters() {
  const session = await auth();
  if (!session?.user) {
    return null;
  }

  const chapters = await prisma.chapter.findMany({
    include: {
      mangaSeries: {
        select: {
          id: true,
          title: true,
          coverImage: true,
        },
      },
      pages: {
        select: {
          id: true,
        },
      },
    },
    orderBy: {
      uploadDate: 'desc', // Show newest chapters first
    },
  });

  // Add page count to each chapter for convenience
  return chapters.map(chapter => ({
    ...chapter,
    pageCount: chapter.pages.length,
    // Remove the pages array since we only needed it for counting
    pages: undefined,
  }));
}
