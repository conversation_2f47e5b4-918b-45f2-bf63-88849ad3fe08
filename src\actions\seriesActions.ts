"use server";

// Use named import for prisma
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { auth } from '@/auth';
import { uploadCoverImage, deleteImage, extractFilePathFromUrl, isSupabaseStorageUrl } from '@/lib/supabaseStorage';
import fs from 'fs/promises';
import path from 'path';


/**
 * Fetch all manga series
 */
export async function getSeriesList() {
  // No auth check needed for just fetching list data usually,
  // but ensure pages calling this are protected by middleware.
  const series = await prisma.mangaSeries.findMany({
    orderBy: { title: 'asc' },
  });
  return series;
}

/**
 * Fetch a single manga series by ID, including its chapters
 */
export async function getSeriesById(id: string) {
  // No auth check needed here either, assuming calling page is protected.
  const series = await prisma.mangaSeries.findUnique({
    where: { id },
    include: {
      chapters: {
        orderBy: {
          // Assuming chapterNumber is stored as a string that can be numerically sorted
          // Adjust if chapterNumber is a number or needs different sorting
          chapterNumber: 'asc', // Or 'desc' if you want newest first
        },
      },
    },
  });
  return series;
}


/**
 * Create a new manga series
 */
export async function createSeries(formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    throw new Error("Not authenticated");
  }

  const title = formData.get('title')?.toString() || '';
  const description = formData.get('description')?.toString() || null;
  const status = formData.get('status')?.toString() || null;
  const author = formData.get('author')?.toString() || null;
  const artist = formData.get('artist')?.toString() || null;
  const genres = formData.get('genres')?.toString() || null;
  const coverFile = formData.get('coverImage') as File;
  let coverImagePath: string | null = null;

  // Basic validation
  if (!title) {
      throw new Error("Title is required");
  }

  if (coverFile && coverFile.size > 0) {
    // Upload cover image to Supabase Storage
    const uploadResult = await uploadCoverImage(coverFile);

    if (!uploadResult.success) {
      return {
        errors: { coverImage: [uploadResult.error || 'Failed to upload cover image.'] },
        message: 'Cover image upload failed.',
      };
    }

    coverImagePath = uploadResult.url || null;
  }

  try {
      await prisma.mangaSeries.create({
        data: { title, description, status, author, artist, genres, coverImage: coverImagePath }
      });
      revalidatePath('/dashboard/series'); // Revalidate the series list page
      return { success: true, message: "Series created successfully." };
  } catch (error) {
      console.error("Failed to create series:", error);
      // Clean up uploaded file if DB insert fails
      if (coverImagePath && isSupabaseStorageUrl(coverImagePath)) {
          const filePath = extractFilePathFromUrl(coverImagePath);
          if (filePath) {
              const deleteResult = await deleteImage('COVERS', filePath);
              if (!deleteResult.success) {
                  console.error(
                    "Failed to clean up uploaded file after DB error:",
                    deleteResult.error
                  );
              }
          }
      }
      // Return a failure message
      return { success: false, message: "Database error occurred while creating series." };
  }
}

/**
 * Delete a manga series
 */
export async function deleteSeries(formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    throw new Error("Not authenticated");
  }

  const id = formData.get('id')?.toString();
  if (!id) {
    throw new Error("ID is required for deletion");
  }

  // Optional: Get series details to delete associated cover image
  const series = await prisma.mangaSeries.findUnique({ where: { id } });
  const coverPath = series?.coverImage;

  try {
      await prisma.mangaSeries.delete({ where: { id } });

      // Delete cover image after successful DB deletion
      if (coverPath) {
        if (isSupabaseStorageUrl(coverPath)) {
          // Delete from Supabase Storage
          const filePath = extractFilePathFromUrl(coverPath);
          if (filePath) {
            const deleteResult = await deleteImage('COVERS', filePath);
            if (!deleteResult.success) {
              console.error("Failed to delete cover image from Supabase:", deleteResult.error);
            }
          }
        } else {
          // Legacy: Delete from local file system (for migration period)
          try {
            const fullPath = path.join(process.cwd(), 'public', coverPath);
            await fs.unlink(fullPath);
          } catch (deleteError: any) {
            if (deleteError.code !== 'ENOENT') {
              console.error("Failed to delete associated cover image from local storage:", deleteError);
            }
          }
        }
      }

      revalidatePath('/dashboard/series');
      return { success: true, message: "Series deleted successfully." };
  } catch (error) {
      console.error("Failed to delete series:", error);
      // Use a more specific error message
      throw new Error("Database error: Failed to delete series.");
  }
}

/**
 * Update an existing manga series
 */
export async function updateSeries(id: string, formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    return { success: false, message: "Not authenticated" };
  }

  if (!id) {
    return { success: false, message: "Series ID is required for update." };
  }

  const title = formData.get('title')?.toString() || '';
  const description = formData.get('description')?.toString() || null;
  const status = formData.get('status')?.toString() || null;
  const author = formData.get('author')?.toString() || null;
  const artist = formData.get('artist')?.toString() || null;
  const genres = formData.get('genres')?.toString() || null;
  const coverFile = formData.get('coverImage') as File;

  // Basic validation
  if (!title) {
    return { success: false, message: "Title is required" };
  }

  let coverImagePath: string | undefined | null = undefined; // Use undefined to signal no change initially
  let oldCoverPath: string | null = null;
  let newFilePath: string | null = null;

  // Fetch existing series to get old cover path if needed
  const existingSeries = await prisma.mangaSeries.findUnique({ where: { id } });
  if (!existingSeries) {
    return { success: false, message: "Series not found." };
  }
  oldCoverPath = existingSeries.coverImage;

  if (coverFile && coverFile.size > 0) {
    // New file uploaded, process it with Supabase Storage
    const uploadResult = await uploadCoverImage(coverFile);

    if (!uploadResult.success) {
      return {
        success: false,
        message: uploadResult.error || "Failed to save new cover image."
      };
    }

    coverImagePath = uploadResult.url || null;
    newFilePath = uploadResult.path || null; // Keep track of the new file path for potential cleanup
  }

  try {
    const dataToUpdate: any = {
      title,
      description,
      status,
      author,
      artist,
      genres,
    };

    // Only include coverImage in update if a new one was successfully uploaded
    if (coverImagePath !== undefined) {
      dataToUpdate.coverImage = coverImagePath;
    }

    await prisma.mangaSeries.update({
      where: { id },
      data: dataToUpdate,
    });

    // Delete old cover image AFTER successful DB update, if a new one was uploaded
    if (coverImagePath !== undefined && oldCoverPath) {
      if (isSupabaseStorageUrl(oldCoverPath)) {
        // Delete from Supabase Storage
        const oldFilePath = extractFilePathFromUrl(oldCoverPath);
        if (oldFilePath) {
          const deleteResult = await deleteImage('COVERS', oldFilePath);
          if (!deleteResult.success) {
            console.error("Failed to delete old cover image from Supabase:", deleteResult.error);
            // Don't fail the whole operation, but log it
          }
        }
      } else {
        // Legacy: Delete from local file system (for migration period)
        try {
          const fullOldPath = path.join(process.cwd(), 'public', oldCoverPath);
          await fs.unlink(fullOldPath);
        } catch (deleteError: any) {
          if (deleteError.code !== 'ENOENT') {
            console.error("Failed to delete old cover image from local storage:", deleteError);
          }
        }
      }
    }

    revalidatePath('/dashboard/series');
    revalidatePath(`/dashboard/series/${id}/edit`); // Revalidate the edit page
    return { success: true, message: "Series updated successfully." };

  } catch (error) {
    console.error("Failed to update series:", error);

    // Clean up newly uploaded file if DB update fails
    if (newFilePath) {
      const deleteResult = await deleteImage('COVERS', newFilePath);
      if (!deleteResult.success) {
        console.error(
          "Failed to clean up newly uploaded file after DB error:",
          deleteResult.error
        );
      }
    }
    return { success: false, message: "Database error occurred while updating series." };
  }
}
