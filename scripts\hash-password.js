const bcrypt = require('bcrypt');

// Get password from command line argument
const password = process.argv[2];

if (!password) {
    console.log('Usage: node hash-password.js "your_password"');
    process.exit(1);
}

bcrypt.hash(password, 10)
    .then(hash => {
        console.log('Hashed password:');
        console.log(hash);
        console.log('\nCopy this hash and paste it into the password field in Prisma Studio');
    })
    .catch(err => {
        console.error('Error hashing password:', err);
    });
