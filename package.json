{"name": "taroo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "prisma:seed": "ts-node prisma/seed.ts", "db:seed": "pnpm prisma generate && pnpm prisma db seed", "setup:supabase": "node scripts/setup-supabase-storage.js", "migrate:images": "node scripts/migrate-images-to-supabase.js"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "6.10.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.492.0", "next": "15.3.0", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "supabase": "^2.22.12", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.0", "prisma": "^6.10.1", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}