import { prisma } from '@/lib/prisma'; // Fix: Use named import
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { OptimizedImage } from '@/components/ui/optimized-image';

// Force dynamic rendering to avoid static generation issues with database
export const dynamic = 'force-dynamic';

interface SeriesDetailPageProps {
  params: Promise<{
    seriesId: string;
  }>;
}

// Use await on params before destructuring
export default async function SeriesDetailPage({ params }: SeriesDetailPageProps) {
  const { seriesId } = await params;
  const series = await prisma.mangaSeries.findUnique({
    where: { id: seriesId },
    include: {
      chapters: {
        orderBy: {
          // Attempt to sort numerically, falling back to string sort
          // This is complex with string chapter numbers like '10.5', 'Extra'
          // A more robust solution might involve padding or a dedicated numeric field
          chapterNumber: 'asc', // Simple string sort for MVP
        },
        select: {
          id: true,
          chapterNumber: true,
          title: true,
          uploadDate: true,
        },
      },
    },
  });

  if (!series) {
    notFound(); // Show 404 if series doesn't exist
  }

  // Basic genre splitting (assuming comma-separated)
  const genres = series.genres?.split(',').map(g => g.trim()).filter(g => g) || [];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        {/* Left Column: Cover Image */}
        <div className="md:col-span-1">
          <div className="relative w-full aspect-[2/3] rounded-md shadow-lg overflow-hidden">
            <OptimizedImage
              src={series.coverImage}
              alt={`Cover for ${series.title}`}
              fill
              sizesType="cover"
              className="rounded-md"
              fallbackClassName="bg-gray-200 w-full h-full"
              fallbackText="No Cover"
              priority={true}
              usePlaceholder={true}
            />
          </div>
        </div>

        {/* Right Column: Details & Chapters */}
        <div className="md:col-span-3 space-y-6">
          {/* Series Title & Status */}
          <div className="border-b pb-4">
            <h1 className="text-3xl font-bold mb-2">{series.title}</h1>
            {series.status && (
              <Badge variant="secondary">{series.status}</Badge>
            )}
          </div>

          {/* Author & Artist */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-700">
            {series.author && <div><strong>Author:</strong> {series.author}</div>}
            {series.artist && <div><strong>Artist:</strong> {series.artist}</div>}
          </div>

          {/* Genres */}
          {genres.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Genres</h3>
              <div className="flex flex-wrap gap-2">
                {genres.map((genre) => (
                  <Badge key={genre} variant="outline">{genre}</Badge>
                ))}
              </div>
            </div>
          )}

          {/* Description */}
          {series.description && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Description</h3>
              <p className="text-gray-600 whitespace-pre-wrap">{series.description}</p>
            </div>
          )}

          {/* Chapter List */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Chapters</h3>
            {series.chapters.length > 0 ? (
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="chapters">
                  <AccordionTrigger>View Chapters ({series.chapters.length})</AccordionTrigger>
                  <AccordionContent>
                    <ul className="space-y-2 max-h-96 overflow-y-auto pr-2">
                      {series.chapters.map((chapter) => (
                        <li key={chapter.id} className="border-b last:border-b-0 py-2">
                          <Link
                            href={`/read/${series.id}/${chapter.chapterNumber}`}
                            className="flex justify-between items-center hover:bg-gray-100 p-2 rounded transition-colors duration-150"
                          >
                            <div>
                              <span className="font-medium text-blue-700 hover:underline">
                                Chapter {chapter.chapterNumber}
                              </span>
                              {chapter.title && (
                                <span className="text-gray-500 text-sm ml-2 italic">- {chapter.title}</span>
                              )}
                            </div>
                            <span className="text-xs text-gray-500">
                              {new Date(chapter.uploadDate).toLocaleDateString()}
                            </span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ) : (
              <p className="text-gray-500">No chapters uploaded yet.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
