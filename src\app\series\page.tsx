import { Suspense } from 'react';
import { MangaCard } from '@/components/manga/MangaCard';
import { getFilteredSeries, getAllGenres, getAllStatuses } from '@/actions/seriesListActions';
import { SearchInput } from '@/components/search/SearchInput';
import { SeriesFilters } from './SeriesFilters';
import { SeriesPagination } from './SeriesPagination';

// Force dynamic rendering to avoid static generation issues with database
export const dynamic = 'force-dynamic';

// Define the props for the page component
interface SeriesPageProps {
  searchParams: Promise<{
    genre?: string | null;
    status?: string | null;
    sort?: string;
    order?: 'asc' | 'desc';
    page?: string;
  }>;
}

export default async function SeriesPage({ searchParams }: SeriesPageProps) {
  // Parse search params - await them first
  const params = await searchParams;
  const genre = params.genre === '' ? null : params.genre;
  const status = params.status === '' ? null : params.status;
  const sortParam = params.sort || 'title';
  const sortBy = ['title', 'createdAt'].includes(sortParam)
    ? sortParam as 'title' | 'createdAt'
    : 'title';
  const sortOrder = params.order === 'desc' ? 'desc' : 'asc';
  const page = Number(params.page) || 1;

  // Fetch data
  const { series, pagination } = await getFilteredSeries({
    genre,
    status,
    sortBy,
    sortOrder,
    page,
    pageSize: 24,
  });

  // Fetch filter options
  const genres = await getAllGenres();
  const statuses = await getAllStatuses();

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="bg-[#343450] rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Browse Manga</h1>
        <p className="text-gray-300 mb-6">Discover your next favorite series</p>

        <SearchInput
          placeholder="Search by title, author, or genre..."
          className="max-w-xl"
        />
      </div>

      {/* Filters Section */}
      <SeriesFilters
        genres={genres}
        statuses={statuses}
        currentGenre={genre}
        currentStatus={status}
        currentSort={sortBy}
        currentOrder={sortOrder}
      />

      {/* Results Count */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-white">
          {pagination.totalItems} {pagination.totalItems === 1 ? 'Series' : 'Series'}
        </h2>
      </div>

      {/* Series Grid */}
      <Suspense fallback={<div className="text-center py-8">Loading...</div>}>
        {series.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {series.map((manga) => (
              <MangaCard key={manga.id} manga={manga} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-[#343450] rounded-lg">
            <h3 className="text-xl font-semibold mb-2">No series found</h3>
            <p className="text-gray-300">Try adjusting your filters or search criteria</p>
          </div>
        )}
      </Suspense>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <SeriesPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          genre={genre}
          status={status}
          sort={sortBy}
          order={sortOrder}
        />
      )}
    </div>
  );
}
