/**
 * <PERSON><PERSON><PERSON> to set up Supabase Storage buckets for the Taroo manga application
 * Run this script to create the necessary storage buckets and configure policies
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Service role key for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupStorageBuckets() {
  console.log('Setting up Supabase Storage buckets for Taroo...');

  try {
    // Create manga-covers bucket
    console.log('Creating manga-covers bucket...');
    const { data: coversBucket, error: coversError } = await supabase.storage
      .createBucket('manga-covers', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        fileSizeLimit: 10485760, // 10MB
      });

    if (coversError && !coversError.message.includes('already exists')) {
      console.error('Error creating manga-covers bucket:', coversError);
    } else {
      console.log('✓ manga-covers bucket created successfully');
    }

    // Create manga-pages bucket
    console.log('Creating manga-pages bucket...');
    const { data: pagesBucket, error: pagesError } = await supabase.storage
      .createBucket('manga-pages', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        fileSizeLimit: 10485760, // 10MB
      });

    if (pagesError && !pagesError.message.includes('already exists')) {
      console.error('Error creating manga-pages bucket:', pagesError);
    } else {
      console.log('✓ manga-pages bucket created successfully');
    }

    // Set up bucket policies for public access
    console.log('Setting up bucket policies...');
    
    console.log('✓ Buckets created successfully!');
    console.log('\nNext steps:');
    console.log('1. Go to your Supabase dashboard > Storage');
    console.log('2. Configure bucket policies if needed');
    console.log('3. Verify that both buckets are publicly accessible');
    console.log('\nBuckets created:');
    console.log('- manga-covers: For manga series cover images');
    console.log('- manga-pages: For manga chapter page images');

  } catch (error) {
    console.error('Error setting up storage buckets:', error);
    process.exit(1);
  }
}

// Run the setup
setupStorageBuckets()
  .then(() => {
    console.log('\n✅ Supabase Storage setup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
