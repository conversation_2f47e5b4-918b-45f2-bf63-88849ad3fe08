# Taroo - Manga Reader

Taroo is a web application for reading manga online.

## Features Overview

*   Admin authentication with improved error handling and automatic redirection for logged-in users.
*   Admin dashboard for managing manga series (CRUD operations, including cover image uploads).
*   Admin dashboard for uploading new chapters with page images, including page reordering and deletion.
*   Admin dashboard with chapter statistics and management.
*   Public site displaying manga series with filtering and pagination.
*   Public site displaying latest chapter releases.
*   Detailed view for each manga series, listing chapters.
*   Manga reader with single-page and long-strip view modes.
*   Chapter navigation (next/previous chapter, chapter selection dropdown).
*   Search functionality for finding manga series.
*   Popular manga and Updates pages.
*   Dark theme styling with purple accents for improved visual appeal.
*   Static pages (About, Contact, Privacy Policy).
*   Optimized image handling for better performance.

## Tech Stack Used

*   **Framework:** Next.js (App Router)
*   **Language:** TypeScript
*   **Database:** PostgreSQL
*   **ORM:** Prisma
*   **Authentication:** NextAuth.js (v5/beta)
*   **UI Components:** Shadcn/ui
*   **Styling:** Tailwind CSS
*   **Form Handling:** React Hook Form (likely used with Shadcn Form)
*   **Validation:** Zod (likely used with React Hook Form)
*   **Package Manager:** pnpm

## Prerequisites for Setup

*   Node.js
*   pnpm (`npm install -g pnpm`)
*   A running PostgreSQL server instance.

## Getting Started

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd taroo
    ```

2.  **Install dependencies:**
    ```bash
    pnpm install
    ```

3.  **Configure Environment Variables:**
    *   Copy the example environment file: `cp .env.example .env`
    *   Edit the `.env` file and fill in the required values:
        *   `DATABASE_URL`: Your PostgreSQL connection string (e.g., `postgresql://USER:PASSWORD@HOST:PORT/DATABASE`).
        *   `AUTH_SECRET`: A secret key for NextAuth.js. Generate one using: `openssl rand -base64 32`
        *   `NEXTAUTH_URL`: The canonical URL of your deployment (e.g., `http://localhost:3000` for development).
        *   `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL.
        *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key.
        *   `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for admin operations).

4.  **Database Setup:**
    *   Apply database migrations:
        ```bash
        pnpm prisma migrate dev
        ```
    *   (Optional) Seed the database with an initial admin user:
        ```bash
        pnpm prisma db seed
        ```
        *(This will create an initial admin account for accessing the admin dashboard.)*

5.  **Supabase Storage Setup:**
    *   Run the Supabase Storage setup script:
        ```bash
        pnpm run setup:supabase
        ```
    *   This will create the necessary storage buckets for manga images.

6.  **Run the development server:**
    ```bash
    pnpm dev
    ```
    Open [http://localhost:3000](http://localhost:3000) (or your configured `NEXTAUTH_URL`) with your browser to see the result. The admin login is typically at `/login`.

## Building for Production

```bash
pnpm build
```

## Running Production Server

```bash
pnpm start
```

## Deployment to Vercel

To deploy this application to Vercel:

1. Connect your GitHub repository to Vercel
2. Configure the following environment variables in the Vercel dashboard:
   - `DATABASE_URL`: Your PostgreSQL connection string
   - `AUTH_SECRET`: A secret key for NextAuth.js
   - `NEXTAUTH_URL`: The URL of your Vercel deployment (e.g., https://your-app.vercel.app)
   - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
   - `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key

The project includes a `vercel.json` configuration file that ensures Prisma client is properly generated during the build process. Additionally, the build script in `package.json` has been configured to run `prisma generate` before the Next.js build to prevent the "PrismaClientInitializationError" that can occur with Vercel's dependency caching.

## Image Migration

If you're migrating from a previous version that used local file storage, run the migration script:

```bash
pnpm run migrate:images
```

This will:
- Upload existing local images to Supabase Storage
- Update database references to use Supabase Storage URLs
- Preserve the original file organization structure

## Notes

*   **Image Storage:** Uses Supabase Storage for reliable, scalable image hosting that works across all deployment environments.
*   **Error Handling:** Improved error handling with user-friendly messages, particularly for authentication and database operations.
*   **Edge Compatibility:** The application is compatible with Next.js Edge Runtime, with authentication utilities optimized for this environment.
