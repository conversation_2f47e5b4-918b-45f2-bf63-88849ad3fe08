/**
 * Supabase Storage utility functions for handling image uploads and management
 */

import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

// Storage bucket names
export const STORAGE_BUCKETS = {
  COVERS: 'manga-covers',
  PAGES: 'manga-pages',
} as const;

// Allowed image types
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp'
];

// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Validate image file
 */
function validateImageFile(file: File): { isValid: boolean; error?: string } {
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`
    };
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    };
  }

  return { isValid: true };
}

/**
 * Generate a unique filename with proper extension
 */
function generateFileName(originalName: string): string {
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  return `${uuidv4()}.${extension}`;
}

/**
 * Upload cover image to Supabase Storage
 */
export async function uploadCoverImage(file: File): Promise<{ 
  success: boolean; 
  url?: string; 
  path?: string;
  error?: string 
}> {
  try {
    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    // Generate unique filename
    const fileName = generateFileName(file.name);
    const filePath = `covers/${fileName}`;

    // Upload file to Supabase Storage
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKETS.COVERS)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Supabase storage upload error:', error);
      return { success: false, error: `Upload failed: ${error.message}` };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(STORAGE_BUCKETS.COVERS)
      .getPublicUrl(filePath);

    return {
      success: true,
      url: urlData.publicUrl,
      path: filePath
    };

  } catch (error) {
    console.error('Cover image upload error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown upload error' 
    };
  }
}

/**
 * Upload manga page image to Supabase Storage
 */
export async function uploadPageImage(
  file: File, 
  mangaSeriesId: string, 
  chapterNumber: string,
  pageNumber: number
): Promise<{ 
  success: boolean; 
  url?: string; 
  path?: string;
  error?: string 
}> {
  try {
    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    // Sanitize chapter number for file path
    const sanitizedChapterNumber = chapterNumber.replace(/[^a-zA-Z0-9_.-]/g, '_');
    
    // Generate filename with page number
    const extension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const fileName = `page_${pageNumber.toString().padStart(3, '0')}.${extension}`;
    const filePath = `pages/${mangaSeriesId}/${sanitizedChapterNumber}/${fileName}`;

    // Upload file to Supabase Storage
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKETS.PAGES)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Supabase storage upload error:', error);
      return { success: false, error: `Upload failed: ${error.message}` };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(STORAGE_BUCKETS.PAGES)
      .getPublicUrl(filePath);

    return {
      success: true,
      url: urlData.publicUrl,
      path: filePath
    };

  } catch (error) {
    console.error('Page image upload error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown upload error' 
    };
  }
}

/**
 * Delete image from Supabase Storage
 */
export async function deleteImage(
  bucket: keyof typeof STORAGE_BUCKETS, 
  filePath: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const bucketName = STORAGE_BUCKETS[bucket];
    
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      console.error('Supabase storage delete error:', error);
      return { success: false, error: `Delete failed: ${error.message}` };
    }

    return { success: true };

  } catch (error) {
    console.error('Image delete error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown delete error' 
    };
  }
}

/**
 * Get public URL for an image
 */
export function getImagePublicUrl(
  bucket: keyof typeof STORAGE_BUCKETS, 
  filePath: string
): string {
  const bucketName = STORAGE_BUCKETS[bucket];
  
  const { data } = supabase.storage
    .from(bucketName)
    .getPublicUrl(filePath);

  return data.publicUrl;
}

/**
 * Extract file path from Supabase Storage URL
 */
export function extractFilePathFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // Find the bucket name and extract the path after it
    const bucketIndex = pathParts.findIndex(part => 
      Object.values(STORAGE_BUCKETS).includes(part as any)
    );
    
    if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
      return pathParts.slice(bucketIndex + 1).join('/');
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Check if URL is a Supabase Storage URL
 */
export function isSupabaseStorageUrl(url: string): boolean {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes('supabase') && 
           urlObj.pathname.includes('/storage/v1/object/public/');
  } catch {
    return false;
  }
}
