import { PrismaClient } from '../generated/prisma';

declare global {
  // allow global `var` declarations
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

// Create a custom error class for database connection issues
export class DatabaseConnectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DatabaseConnectionError';
  }
}

// Initialize Prisma client with error handling
function createPrismaClient() {
  // For serverless environments, we need to configure connection pooling
  const client = new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

  return client;
}

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

// In production, it's best to not use a global variable
const prismaGlobal = global as unknown as { prisma: PrismaClient };

export const prisma = prismaGlobal.prisma || createPrismaClient();

// Save client reference in development to prevent multiple instances
if (process.env.NODE_ENV !== 'production') prismaGlobal.prisma = prisma;
