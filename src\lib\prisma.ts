import { PrismaClient } from '../generated/prisma';

// Create a custom error class for database connection issues
export class DatabaseConnectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DatabaseConnectionError';
  }
}

// Declare global variable for Prisma client
declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

// Initialize Prisma client with error handling
function createPrismaClient() {
  const client = new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    // Add connection timeout for serverless environments
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

  return client;
}

// Create a singleton Prisma client
// In development, use global variable to prevent multiple instances during hot reloads
// In production, create a new instance each time
export const prisma = (() => {
  if (process.env.NODE_ENV === 'production') {
    return createPrismaClient();
  }

  if (!global.__prisma) {
    global.__prisma = createPrismaClient();
  }

  return global.__prisma;
})();

// Helper function to handle database connection errors gracefully
export async function withDatabaseErrorHandling<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error('Database operation failed:', error);

    // If it's a connection error during build, return fallback or throw a more specific error
    if (error instanceof Error && error.message.includes('prepared statement')) {
      throw new DatabaseConnectionError(
        'Database connection issue during build. This might be due to connection pooling conflicts in serverless environments.'
      );
    }

    if (fallback !== undefined) {
      return fallback;
    }

    throw error;
  }
}
