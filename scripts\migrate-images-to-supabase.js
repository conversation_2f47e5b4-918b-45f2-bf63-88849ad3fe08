/**
 * Migration script to move existing local images to Supabase Storage
 * and update database references accordingly
 */

const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('../src/generated/prisma');
const fs = require('fs/promises');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const prisma = new PrismaClient();

// Storage bucket names
const STORAGE_BUCKETS = {
  COVERS: 'manga-covers',
  PAGES: 'manga-pages',
};

/**
 * Upload a local file to Supabase Storage
 */
async function uploadFileToSupabase(localFilePath, bucket, storagePath) {
  try {
    // Check if local file exists
    await fs.access(localFilePath);
    
    // Read the file
    const fileBuffer = await fs.readFile(localFilePath);
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(storagePath, fileBuffer, {
        cacheControl: '3600',
        upsert: true // Allow overwriting if file exists
      });

    if (error) {
      return { success: false, error: error.message };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(storagePath);

    return { success: true, url: urlData.publicUrl };

  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Migrate cover images
 */
async function migrateCoverImages() {
  console.log('Migrating cover images...');
  
  const results = [];
  const storageBasePath = process.env.IMAGE_STORAGE_PATH || path.join(process.cwd(), 'public', 'uploads');
  const coversPath = path.join(storageBasePath, 'covers');
  
  // Get all series with cover images
  const series = await prisma.mangaSeries.findMany({
    where: {
      coverImage: {
        not: null
      }
    }
  });

  for (const seriesItem of series) {
    if (!seriesItem.coverImage) continue;
    
    // Skip if already a Supabase URL
    if (seriesItem.coverImage.startsWith('http')) {
      console.log(`Skipping ${seriesItem.title} - already using remote URL`);
      continue;
    }

    // Construct local file path
    let localFilePath;
    if (seriesItem.coverImage.startsWith('/uploads/covers/')) {
      localFilePath = path.join(process.cwd(), 'public', seriesItem.coverImage);
    } else if (seriesItem.coverImage.startsWith('covers/')) {
      localFilePath = path.join(coversPath, seriesItem.coverImage.substring('covers/'.length));
    } else {
      localFilePath = path.join(coversPath, path.basename(seriesItem.coverImage));
    }

    // Generate storage path
    const fileName = path.basename(localFilePath);
    const storagePath = `covers/${fileName}`;

    // Upload to Supabase
    const uploadResult = await uploadFileToSupabase(localFilePath, STORAGE_BUCKETS.COVERS, storagePath);
    
    if (uploadResult.success && uploadResult.url) {
      // Update database
      try {
        await prisma.mangaSeries.update({
          where: { id: seriesItem.id },
          data: { coverImage: uploadResult.url }
        });
        
        results.push({
          success: true,
          originalPath: seriesItem.coverImage,
          newUrl: uploadResult.url
        });
        
        console.log(`✓ Migrated cover for "${seriesItem.title}"`);
      } catch (dbError) {
        results.push({
          success: false,
          originalPath: seriesItem.coverImage,
          error: `Database update failed: ${dbError}`
        });
      }
    } else {
      results.push({
        success: false,
        originalPath: seriesItem.coverImage,
        error: uploadResult.error
      });
      console.log(`✗ Failed to migrate cover for "${seriesItem.title}": ${uploadResult.error}`);
    }
  }

  return results;
}

/**
 * Migrate page images
 */
async function migratePageImages() {
  console.log('Migrating page images...');
  
  const results = [];
  const storageBasePath = process.env.IMAGE_STORAGE_PATH || path.join(process.cwd(), 'public', 'uploads');
  
  // Get all pages with image paths
  const pages = await prisma.page.findMany({
    include: {
      chapter: {
        include: {
          mangaSeries: {
            select: { id: true, title: true }
          }
        }
      }
    }
  });

  for (const page of pages) {
    if (!page.imagePath) continue;
    
    // Skip if already a Supabase URL
    if (page.imagePath.startsWith('http')) {
      console.log(`Skipping page ${page.pageNumber} - already using remote URL`);
      continue;
    }

    // Construct local file path
    const localFilePath = path.join(storageBasePath, page.imagePath);
    
    // Generate storage path
    const sanitizedChapterNumber = page.chapter.chapterNumber.replace(/[^a-zA-Z0-9_.-]/g, '_');
    const fileName = path.basename(page.imagePath);
    const storagePath = `pages/${page.chapter.mangaSeriesId}/${sanitizedChapterNumber}/${fileName}`;

    // Upload to Supabase
    const uploadResult = await uploadFileToSupabase(localFilePath, STORAGE_BUCKETS.PAGES, storagePath);
    
    if (uploadResult.success && uploadResult.url) {
      // Update database
      try {
        await prisma.page.update({
          where: { id: page.id },
          data: { imagePath: uploadResult.url }
        });
        
        results.push({
          success: true,
          originalPath: page.imagePath,
          newUrl: uploadResult.url
        });
        
        console.log(`✓ Migrated page ${page.pageNumber} of chapter ${page.chapter.chapterNumber} (${page.chapter.mangaSeries.title})`);
      } catch (dbError) {
        results.push({
          success: false,
          originalPath: page.imagePath,
          error: `Database update failed: ${dbError}`
        });
      }
    } else {
      results.push({
        success: false,
        originalPath: page.imagePath,
        error: uploadResult.error
      });
      console.log(`✗ Failed to migrate page ${page.pageNumber}: ${uploadResult.error}`);
    }
  }

  return results;
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('Starting image migration to Supabase Storage...');
  
  try {
    // Migrate cover images
    const coverResults = await migrateCoverImages();
    
    // Migrate page images
    const pageResults = await migratePageImages();
    
    // Summary
    const totalCoverSuccess = coverResults.filter(r => r.success).length;
    const totalCoverFailed = coverResults.filter(r => !r.success).length;
    const totalPageSuccess = pageResults.filter(r => r.success).length;
    const totalPageFailed = pageResults.filter(r => !r.success).length;
    
    console.log('\n=== Migration Summary ===');
    console.log(`Cover Images: ${totalCoverSuccess} successful, ${totalCoverFailed} failed`);
    console.log(`Page Images: ${totalPageSuccess} successful, ${totalPageFailed} failed`);
    console.log(`Total: ${totalCoverSuccess + totalPageSuccess} successful, ${totalCoverFailed + totalPageFailed} failed`);
    
    if (totalCoverFailed > 0 || totalPageFailed > 0) {
      console.log('\nFailed migrations:');
      [...coverResults, ...pageResults]
        .filter(r => !r.success)
        .forEach(r => console.log(`- ${r.originalPath}: ${r.error}`));
    }
    
    console.log('\n✅ Migration completed!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
runMigration();
